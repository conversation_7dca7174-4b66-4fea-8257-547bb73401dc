package com.chaos.im.chat.domain;

import com.chaos.keep.alive.common.core.domain.BaseDomain;
import lombok.Getter;
import lombok.Setter;


/**
 * <AUTHOR>
 */
@Getter
@Setter
public class ChatMemberVO extends BaseDomain {

    private Long id;

    private Integer type;

    private String peerId;

    private String memberId;

    private String nickname;

    private String avatar;

    private Long activeTime;

    private LastMessageVO lastMessage;

    private long unReadCount;

    private String ex;
}

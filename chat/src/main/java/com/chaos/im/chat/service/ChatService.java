package com.chaos.im.chat.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chaos.im.chat.domain.*;
import org.springframework.transaction.annotation.Transactional;

import java.util.concurrent.ExecutionException;

public interface ChatService {

    IPage<ChatMemberDTO> pageChat(ChatListRequest chatListRequest) throws ExecutionException, InterruptedException;

    @Transactional
    Long save(ChatDTO chatDTO, Long chatId);

    CreateChatResponse  createChat(CreateChatRequest request);

    CreateChatResponse createGroupChat(CreateGroupChatRequest request);

    @Transactional
    void deleteByChatId(Long chatId);

    void delete(Long chatId, String operatorNo);

    Long getChatIdByGroupId(Long groupId);
}

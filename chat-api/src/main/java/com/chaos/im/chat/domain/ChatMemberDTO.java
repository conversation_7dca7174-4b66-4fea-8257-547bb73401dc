package com.chaos.im.chat.domain;

import com.chaos.keep.alive.common.core.domain.BaseDomain;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class ChatMemberDTO extends BaseDomain {

    private Long id;

    private Long chatId;

    private Integer type;

    private String peerId;

    private String memberId;

    private String nickname;

    private String avatar;

    private LocalDateTime activeTime;

    private LastMessageDTO lastMessage;

    private String ex;
}

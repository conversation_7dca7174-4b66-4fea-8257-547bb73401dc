package com.lifekh.gateway.tcp.route.command;

import cn.hutool.json.JSONUtil;
import com.chaos.keep.alive.common.core.constant.CommandType;
import com.chaos.keep.alive.common.core.domain.JsonResult;
import com.chaos.keep.alive.common.im.domain.JsonCommand;
import com.chaos.keep.alive.common.protobuf.Command;
import com.chaos.keep.alive.common.protobuf.Result;
import com.lifekh.gateway.tcp.client.ClientManager;
import com.lifekh.gateway.tcp.manager.TrafficManager;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.codec.http.websocketx.TextWebSocketFrame;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OnlineClientCommandHandler extends AbstractClientCommandHandler {

    private final ClientManager clientManager;

    @Autowired
    private final TrafficManager trafficManager;
    @SneakyThrows
    @Override
    public void handleProtoCommand(Command command, ChannelHandlerContext ctx) {
        SocketChannel clientChannel = clientManager.getChannel(command.getOperatorNo(), command.getDeviceId());

        handleResult(command, clientChannel);

        clientChannel.writeAndFlush(command);
    }

    @SneakyThrows
    @Override
    public void handleJsonCommand(Command command, ChannelHandlerContext ctx) {
        SocketChannel clientChannel = clientManager.getChannel(command.getOperatorNo(), command.getDeviceId());
        Result result = Result.parseFrom(command.getBody());
        JsonResult jsonResult = new JsonResult();
        BeanUtils.copyProperties(result, jsonResult);
        handleResult(command, clientChannel);
        JsonCommand jsonCommand = JsonCommand.convert(command);
        jsonCommand.setBody(JSONUtil.toJsonStr(jsonResult));
        clientChannel.writeAndFlush(new TextWebSocketFrame(JSONUtil.toJsonStr(jsonCommand)));
    }

    @SneakyThrows
    private void handleResult(Command command, SocketChannel clientChannel) {
        Result result = Result.parseFrom(command.getBody());
        log.info("发送鉴权响应,op:{} ,success:{}",command.getOperatorNo(),result.getSuccess());
        if (!result.getSuccess()) {
            clientChannel.writeAndFlush(command);
            clientManager.removeChannel(clientChannel);
            trafficManager.addAuthCount(command.getOperatorNo()+"-"+command.getDeviceId(),1);
            log.info("客户端:{}鉴权失败,主动断开客户端连接",command.getOperatorNo()+"-"+command.getDeviceId());
            Thread.sleep(2000);
            clientChannel.close();

        }
    }


    @Override
    public int getType() {
        return CommandType.COMMAND_ONLINE;
    }
}

keep-alive:
    heartbeat:
        heartbeatInterval: 2000
        readTimeout: 60000
    metrics:
        authInterval: 60000
        authLimit: 10
    port: 38888
    protocol: ws
    routeServers: 'null'
    serverId: gateway-tcp-1
    ssl:
        enabled: false  # 是否启用SSL/WSS
        # 证书文件配置（二选一）
        # certPath: /path/to/cert.pem
        # keyPath: /path/to/private.key
        # keyPassword: your_key_password
        # 密钥库配置（二选一）
        # keystorePath: /path/to/keystore.jks
        # keystorePassword: your_keystore_password
        # keystoreType: JKS
    zk:
        enable: true
        intervalTime: 1000
        retry: 3
        zkServer: *************:2181
logging:
    level:
        root: info
server:
    port: 9094
spring:
    application:
        name: gateway-tcp
    main:
        allow-bean-definition-overriding: true
        banner-mode: 'off'
    redis:
        host: *************
        port: 6379
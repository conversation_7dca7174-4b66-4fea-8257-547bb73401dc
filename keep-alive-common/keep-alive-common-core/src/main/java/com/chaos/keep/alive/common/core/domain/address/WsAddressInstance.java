package com.chaos.keep.alive.common.core.domain.address;

import lombok.Getter;

public class WsAddressInstance extends AddressInstance{

    @Getter
    private String contextPath = "ws";

    @Getter
    private String protocal = "ws";

    public WsAddressInstance(String serverId,String protocal, String ip, int port, String contextPath) {
        super(serverId, ip, port);
        this.contextPath = contextPath;
        this.protocal = protocal;
    }


    public String getWsAddress() {
        return String.format("ws://%s:%d/%s", getIp(), getPort(), getContextPath());
    }
}

package com.chaos.keep.alive.client.tcp;

import com.chaos.keep.alive.client.config.PropertiesUtils;
import com.chaos.keep.alive.common.core.constant.CommandType;
import com.chaos.keep.alive.common.protobuf.Command;
import io.netty.channel.ChannelHandlerContext;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 */
@Slf4j
public class ReportLocationCommandHandler extends AbstractCommandHandler {


    public ReportLocationCommandHandler(ThreadPoolExecutor executor) {
        super(executor);
    }

    @Override
    @SneakyThrows
    public void handleCommand(Command command, ChannelHandlerContext ctx, TcpClient tcpClient) {
        tcpClient.handleServerCommand(command);
    }

    @Override
    public int getType() {
        return CommandType.COMMAND_ONLINE;
    }
}

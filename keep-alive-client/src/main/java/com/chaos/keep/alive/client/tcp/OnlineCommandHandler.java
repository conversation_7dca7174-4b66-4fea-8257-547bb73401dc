package com.chaos.keep.alive.client.tcp;

import com.chaos.keep.alive.client.config.PropertiesUtils;
import com.chaos.keep.alive.common.core.constant.CommandType;
import com.chaos.keep.alive.common.protobuf.Command;
import com.chaos.keep.alive.common.protobuf.Result;
import io.netty.channel.ChannelHandlerContext;
import io.netty.util.concurrent.ScheduledFuture;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
public class OnlineCommandHandler extends AbstractCommandHandler {

    private final int heartbeatInterval = PropertiesUtils.getHeartbeatInterval();

    public OnlineCommandHandler(ThreadPoolExecutor executor) {
        super(executor);
    }

    @Override
    @SneakyThrows
    public void handleCommand(Command command, ChannelHandlerContext ctx, TcpClient tcpClient) {
        Result result = Result.parseFrom(command.getBody());
        if (result.getSuccess()) {
            log.info("鉴权成功,客户端[{}:{}]在网关[{}]上线成功...", command.getOperatorNo(), command.getDeviceId(), tcpClient.getAddressInstance().getServerId());
            // 开启心跳调度
            ScheduledFuture<?> scheduledFuture = ctx.executor().scheduleAtFixedRate(() -> {
                if (ctx.channel().isActive()) {
                    log.debug("客户端[{}:{}]发送心跳给[{}]", command.getOperatorNo(), command.getDeviceId(),tcpClient.getAddressInstance().getServerId());
                    Command newCommand = Command.newBuilder().setOperatorNo(command.getOperatorNo()).setDeviceId(command.getDeviceId()).setBizType(CommandType.COMMAND_HEARTBEAT).build();
                    ctx.writeAndFlush(newCommand);
                }
            }, 0, heartbeatInterval, TimeUnit.MILLISECONDS);
            tcpClient.ctx = ctx;
            tcpClient.scheduledFuture = scheduledFuture;
//            tcpClient.retryCommandExecutor.start();
//            tcpClient.retryMessageExecutor.start();
            tcpClient.isConnected.compareAndSet(false,true);
        }else{
            //鉴权不成功应该要下线
            log.info("鉴权不成功");
        }
        tcpClient.handleServerCommand(command);

    }

    @Override
    public int getType() {
        return CommandType.COMMAND_ONLINE;
    }
}

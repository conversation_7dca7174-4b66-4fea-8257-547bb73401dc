package com.chaos.keep.alive.client.http;

import com.chaos.keep.alive.client.config.PropertiesUtils;
import com.chaos.keep.alive.client.tcp.TcpClient;
import com.chaos.keep.alive.common.core.util.ResultHelper;
import com.outstanding.framework.core.ResponseDTO;

/**
 * <AUTHOR>
 */
public class C2gRemote extends Remote {

    private final static String URL = PropertiesUtils.getWebGatewayUrl() + "/api/iplist";

    public C2gRemote(TcpClient tcpClient) {
        super(tcpClient);
    }

    public ResponseDTO<?> fetch() {
        return ResultHelper.ok();
    }
}

package com.chaos.keep.alive.client.ws;

import com.chaos.keep.alive.common.core.constant.CommandType;
import com.chaos.keep.alive.common.im.domain.JsonCommand;

import java.util.TimerTask;

public class WsHeartbeatTimerTask extends TimerTask {
    WebsocketClient client;

    public WsHeartbeatTimerTask(WebsocketClient client) {
        this.client = client;
    }

    @Override
    public void run() {
        JsonCommand command = new JsonCommand();
        command.setOperatorNo(command.getOperatorNo());
        command.setDeviceId(command.getDeviceId());
        command.setBizType(CommandType.COMMAND_HEARTBEAT);

        try {
            client.sendMessage(command);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}

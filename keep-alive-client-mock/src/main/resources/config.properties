web.gateway.url=ws://localhost:38888/ws
#web.gateway.url=http://svc-lifekh-go-service-gonow-ka-iplist-sit.lifekh-go-sit.svc.cluster.local:8080
#id.server.url=http://localhost:8100/id-server
#id.server.url=http://**************:8080/id-server
id.server.url=http://*************:8080/id-server
# FETCH_C2G_OFFLINE_MESSAGE_URL
fetch.c2g.offline.message.url=http://127.0.0.1:8096/c2g/fetch/offline/msg
fetch.c2c.offline.message.url=http://127.0.0.1:8094/c2c/fetch/offline/msg
# FETCH_C2G_LAST_ACK
fetch.c2g.last.ack.url=http://127.0.0.1:8096/c2g/fetch/lastMessageAck
fetch.c2c.last.ack.url=http://127.0.0.1:8094/c2c/fetch/lastMessageAck
id.cache.count=2000
heartbeat.interval=5000
connect.max.retry=-1
command.max.retry=3
message.ack.waitTime=600000
#ka.gateway.address=gateway-tcp:svc-lifekh-go-service-gonow-gateway-tcp-sit.lifekh-go-sit.svc.cluster.local:38888
#ka.gateway.address=gateway-tcp:localhost:38888
#ka.gateway.ws.address=gateway-tcp:wss:localhost:38888/ws
ka.gateway.ws.address=gateway-tcp:ws:localhost:38888/ws
#ka.gateway.ws.address=gateway-tcp:ws:*************:38889/ws
#ka.gateway.ws.address=gateway-tcp:ws:imws-uat.lifekh.com:31008/ws
#ka.gateway.ws.address=gateway-tcp:ws:imws-sit.lifekh.com:30008/ws
#ka.gateway.ws.address=gateway-tcp:ws:*************:38888/ws
#ka.gateway.address=gateway-tcp:gogw.lifekh.com:30888
#ka.gateway.address=gateway-tcp:gogw-sit.lifekh.com:30888
#ka.gateway.address=gateway-tcp:gogw-uat.lifekh.com:31888
#ka.gateway.address=gateway-tcp:************:38888
#ka.gateway.address=gateway-tcp:svc-lifekh-go-service-gonow-gateway-tcp-uat.lifekh-go-uat.svc.cluster.local:38888

